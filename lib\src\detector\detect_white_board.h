#ifndef CZCV_DETECT_WHITE_BOARD_H
#define CZCV_DETECT_WHITE_BOARD_H
#include <vector>
#include <string>
#include <fstream>

#include <opencv2/opencv.hpp>
#include <base/common.h>
#include <base/status.h>
#include <base/abstract_model.h>
#include <detector/base_detector.h>
#include <tracker/base_tracker.h>
#include <detector/yolox_person_det.h>
#ifdef _WIN32
namespace czcv_camera
{
	typedef struct 
	{
		cv::Mat image;  //rgb格式图像
		BboxF person_box;  //人像检测框，
		BboxF hand_box;  //手势检测框
	}stWhiteBoardInfo;

	typedef struct 
	{
		bool bfind;  //是否检测到白板
		BboxF white_board_box;  //白板位置框
	}stWhiteBoardResult;

    class Detect_White_Board: public AbstarctModel
    {
	public:
        Detect_White_Board() {};
		~Detect_White_Board() {};
        Status release() { return CZCV_OK; }

        void  conf_thres(float v){_confThres = v;}
        float conf_thres() const{return  _confThres;}

        void  nms_thres(float v){_nmsThres   = v;}
        float nms_thres() const {return  _nmsThres;}

		void set_primary(bool v) {_is_primary = v;}
		bool is_primary() const {return _is_primary;}

        Status init(std::vector<std::string> modelConfig) { return CZCV_OK; }
        Status run(stWhiteBoardInfo & info, stWhiteBoardResult & result) { return CZCV_OK; }
		
	private:
        float _confThres = 0.1f;
        float _nmsThres = 0.7f;
        bool _is_primary = false;
	};
}
#else
#include "rknn_api.h"

namespace czcv_camera
{
	typedef struct 
	{
		cv::Mat image;  //rgb格式图像
		BboxF person_box;  //人像检测框，
		BboxF hand_box;  //手势检测框
	}stWhiteBoardInfo;


	typedef struct 
	{
		bool bfind;  //是否检测到白板
		BboxF white_board_box;  //白板位置框
	}stWhiteBoardResult;

    class Detect_White_Board: public AbstarctModel
    {
	public:
        Detect_White_Board() {};
		~Detect_White_Board() {};
        Status release() 
		{
			int ret = rknn_destroy(ctx);
			return CZCV_OK;
		}

        void  conf_thres(float v){_confThres = v;}
        float conf_thres() const{return  _confThres;}

        void  nms_thres(float v){_nmsThres   = v;}
        float nms_thres() const {return  _nmsThres;}

		void set_primary(bool v) {_is_primary = v;}
		bool is_primary() const {return _is_primary;}

        Status init(std::vector<std::string> modelConfig);

        Status run(stWhiteBoardInfo & info, stWhiteBoardResult & result);
	private:
        int _inputWidth;
		int _inputHeight;
        int _inputChannel;
        float _confThres = 0.1f;
        float _nmsThres = 0.7f;
        int head_num = 3;
		int topK = 50;
		int class_num = 1;
        bool _is_primary = false;
		std::vector<float> meshgrid;
		std::vector<int> strides = {8, 16, 32};
		std::vector<std::vector<int>> map_size = {{80, 80}, {40, 40}, {20, 20}};

        rknn_input_output_num io_num;
		std::vector<rknn_tensor_attr> input_attrs;
		std::vector<rknn_tensor_attr> output_attrs;
		rknn_context ctx;
		
		void GenerateMeshgrid();
		std::vector<DetectBox> TopK(const std::vector<DetectBox>& detectResult);
		std::vector<DetectBox> postprocess(rknn_output* outputs, int img_h, int img_w,float ratio);
		float calculateIoU(const DetectBox& box1, const DetectBox& box2);
		std::vector<DetectBox> applyNMS(const std::vector<DetectBox>& boxes, float nms_threshold);
		cv::Mat letter_box(const cv::Mat& im, const cv::Size& target_size, float& ratio,float& dw,float& dh,const cv::Scalar& pad_color = cv::Scalar(0, 0, 0));
	};
}
#endif
#endif //CZCV_DETECT_WHITE_BOARD_H