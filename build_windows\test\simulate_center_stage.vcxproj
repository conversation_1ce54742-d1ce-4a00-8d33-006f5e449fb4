﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{2F2743E8-CBCF-3D1F-A93A-1B823651AE6C}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>simulate_center_stage</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program\Project\project\czcv_camera_new\output\x86\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">simulate_center_stage.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">simulate_center_stage</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Program\Project\project\czcv_camera_new\output\x86\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">simulate_center_stage.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">simulate_center_stage</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Program\Project\project\czcv_camera_new\output\x86\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">simulate_center_stage.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">simulate_center_stage</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Program\Project\project\czcv_camera_new\output\x86\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">simulate_center_stage.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">simulate_center_stage</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/windows/opencv4.5.1/include" -std=c++17 -fno-omit-frame-pointer -fasynchronous-unwind-tables</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_WARNINGS;BUILDING_DLL;JSON_SUPPORT;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS;BUILDING_DLL;JSON_SUPPORT;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_calib3d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_core451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_dnn451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_features2d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_flann451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_gapi451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_highgui451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgcodecs451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgproc451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_ml451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_objdetect451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_photo451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_stitching451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_video451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_videoio451.lib;..\lib\Debug\czcv_camera.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_gapi451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\ade.lib;wsock32.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_highgui451.lib;comctl32.lib;gdi32.lib;ole32.lib;setupapi.lib;ws2_32.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_ml451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_objdetect451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\quirc.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_photo451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_stitching451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_video451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_calib3d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_dnn451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libprotobuf.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_features2d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_flann451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_videoio451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgcodecs451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libjpeg-turbo.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libwebp.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libpng.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libtiff.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libopenjp2.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\IlmImf.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgproc451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_core451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\zlib.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\ittnotify.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\lib\glog.lib;-Wl,--whole-archive;-Wl,--no-whole-archive;-lpthread;-fopenmp;-lrt;-ldl;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Program/Project/project/czcv_camera_new/build_windows/test/Debug/simulate_center_stage.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Program/Project/project/czcv_camera_new/output/x86/bin/Debug/simulate_center_stage.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/windows/opencv4.5.1/include" -std=c++17 -fno-omit-frame-pointer -fasynchronous-unwind-tables</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_WARNINGS;BUILDING_DLL;JSON_SUPPORT;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_WARNINGS;BUILDING_DLL;JSON_SUPPORT;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_calib3d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_core451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_dnn451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_features2d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_flann451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_gapi451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_highgui451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgcodecs451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgproc451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_ml451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_objdetect451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_photo451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_stitching451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_video451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_videoio451.lib;..\lib\Release\czcv_camera.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_gapi451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\ade.lib;wsock32.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_highgui451.lib;comctl32.lib;gdi32.lib;ole32.lib;setupapi.lib;ws2_32.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_ml451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_objdetect451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\quirc.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_photo451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_stitching451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_video451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_calib3d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_dnn451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libprotobuf.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_features2d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_flann451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_videoio451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgcodecs451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libjpeg-turbo.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libwebp.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libpng.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libtiff.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libopenjp2.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\IlmImf.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgproc451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_core451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\zlib.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\ittnotify.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\lib\glog.lib;-Wl,--whole-archive;-Wl,--no-whole-archive;-lpthread;-fopenmp;-lrt;-ldl;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Program/Project/project/czcv_camera_new/build_windows/test/Release/simulate_center_stage.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Program/Project/project/czcv_camera_new/output/x86/bin/Release/simulate_center_stage.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/windows/opencv4.5.1/include" -std=c++17 -fno-omit-frame-pointer -fasynchronous-unwind-tables</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;BUILDING_DLL;JSON_SUPPORT;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;BUILDING_DLL;JSON_SUPPORT;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_calib3d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_core451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_dnn451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_features2d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_flann451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_gapi451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_highgui451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgcodecs451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgproc451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_ml451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_objdetect451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_photo451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_stitching451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_video451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_videoio451.lib;..\lib\MinSizeRel\czcv_camera.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_gapi451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\ade.lib;wsock32.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_highgui451.lib;comctl32.lib;gdi32.lib;ole32.lib;setupapi.lib;ws2_32.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_ml451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_objdetect451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\quirc.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_photo451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_stitching451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_video451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_calib3d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_dnn451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libprotobuf.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_features2d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_flann451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_videoio451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgcodecs451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libjpeg-turbo.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libwebp.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libpng.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libtiff.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libopenjp2.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\IlmImf.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgproc451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_core451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\zlib.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\ittnotify.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\lib\glog.lib;-Wl,--whole-archive;-Wl,--no-whole-archive;-lpthread;-fopenmp;-lrt;-ldl;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Program/Project/project/czcv_camera_new/build_windows/test/MinSizeRel/simulate_center_stage.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Program/Project/project/czcv_camera_new/output/x86/bin/MinSizeRel/simulate_center_stage.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/windows/opencv4.5.1/include" -std=c++17 -fno-omit-frame-pointer -fasynchronous-unwind-tables</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;BUILDING_DLL;JSON_SUPPORT;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;BUILDING_DLL;JSON_SUPPORT;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_calib3d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_core451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_dnn451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_features2d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_flann451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_gapi451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_highgui451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgcodecs451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgproc451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_ml451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_objdetect451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_photo451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_stitching451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_video451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_videoio451.lib;..\lib\RelWithDebInfo\czcv_camera.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_gapi451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\ade.lib;wsock32.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_highgui451.lib;comctl32.lib;gdi32.lib;ole32.lib;setupapi.lib;ws2_32.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_ml451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_objdetect451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\quirc.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_photo451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_stitching451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_video451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_calib3d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_dnn451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libprotobuf.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_features2d451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_flann451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_videoio451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgcodecs451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libjpeg-turbo.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libwebp.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libpng.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libtiff.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\libopenjp2.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\IlmImf.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_imgproc451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\opencv_core451.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\zlib.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\ittnotify.lib;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\lib\glog.lib;-Wl,--whole-archive;-Wl,--no-whole-archive;-lpthread;-fopenmp;-lrt;-ldl;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Program/Project/project/czcv_camera_new/build_windows/test/RelWithDebInfo/simulate_center_stage.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Program/Project/project/czcv_camera_new/output/x86/bin/RelWithDebInfo/simulate_center_stage.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Program\Project\project\czcv_camera_new\test\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/Program/Project/project/czcv_camera_new/test/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SD:/Program/Project/project/czcv_camera_new -BD:/Program/Project/project/czcv_camera_new/build_windows --check-stamp-file D:/Program/Project/project/czcv_camera_new/build_windows/test/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program\Project\project\czcv_camera_new\build_windows\test\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/Program/Project/project/czcv_camera_new/test/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SD:/Program/Project/project/czcv_camera_new -BD:/Program/Project/project/czcv_camera_new/build_windows --check-stamp-file D:/Program/Project/project/czcv_camera_new/build_windows/test/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Program\Project\project\czcv_camera_new\build_windows\test\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/Program/Project/project/czcv_camera_new/test/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SD:/Program/Project/project/czcv_camera_new -BD:/Program/Project/project/czcv_camera_new/build_windows --check-stamp-file D:/Program/Project/project/czcv_camera_new/build_windows/test/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Program\Project\project\czcv_camera_new\build_windows\test\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/Program/Project/project/czcv_camera_new/test/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SD:/Program/Project/project/czcv_camera_new -BD:/Program/Project/project/czcv_camera_new/build_windows --check-stamp-file D:/Program/Project/project/czcv_camera_new/build_windows/test/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Program\Project\project\czcv_camera_new\build_windows\test\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\Program\Project\project\czcv_camera_new\build_windows\ZERO_CHECK.vcxproj">
      <Project>{74EF2365-105C-36A3-AD33-0DBDF5B4175D}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\Program\Project\project\czcv_camera_new\build_windows\lib\czcv_camera.vcxproj">
      <Project>{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}</Project>
      <Name>czcv_camera</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>