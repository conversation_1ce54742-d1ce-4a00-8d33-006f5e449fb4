﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\base\abstract_model.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\base\aes256.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\base\common.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\base\macro.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\base\mem_allocator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\base\nms.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\base\status.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\cam_dewarper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\center_stage_api.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\center_stage_capi.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\person_viewer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\config\config_setter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\libopencl-stub\src\libopencl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\utils\hungarian_match.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Program\Project\project\czcv_camera_new\lib\src\utils\lapjv.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Program\Project\project\czcv_camera_new\lib\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4DF39374-150A-3DD0-90AD-B779DAD57D49}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
