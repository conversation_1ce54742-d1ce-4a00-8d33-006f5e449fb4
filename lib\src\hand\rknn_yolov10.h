// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_HAND_RKNN_YOLOV10_H
#define CZCV_HAND_RKNN_YOLOV10_H

#include <vector>
#include <string>
#include <fstream>

#include <opencv2/opencv.hpp>
#include <base/common.h>
#include <base/status.h>
#include <base/abstract_model.h>
#include <detector/base_detector.h>
#include <tracker/base_tracker.h>
#include <detector/yolox_person_det.h>
#ifdef _WIN32

namespace czcv_camera
{
	typedef struct 
	{
		Rectf rect;
		float score;
		int label;
	} DetectionResult;
	
    class Yolov10RKNN: public AbstarctModel
    {
    public:
		Yolov10RKNN() {};
		~Yolov10RKNN() {};
		Status release() { return CZCV_OK; }
		
        Status run(TrackerInputOutput &inputOutput) { return CZCV_OK; }
		Status run_sub(const cv::Mat & bgr, std::vector<DetectionResult>& results_filtered) { return CZCV_OK; }
		Status run_sub_rga(float scale, std::vector<DetectionResult>& results_filtered) { return CZCV_OK; }
		Status run_sub_rga_qat(float scale, std::vector<DetectionResult>& results_filtered) { return CZCV_OK; }
        Status init(std::vector<std::string> modelConfig,std::shared_ptr<rga_interface_t> &rgaInterfacePtr,czcv_model_type_t modelType) { return CZCV_OK; }
		
		Status processPersonBoxGroup(TrackerInputOutput &inputOutput,
			const std::vector<size_t>& box_indices,
			const cv::Mat& bgr,
			int img_width,
			int img_height) { return CZCV_OK; }

        void conf_thres(float v) {}
        float conf_thres() const { return 0.0f; }

        void nms_thres(float v) {}
        float nms_thres() const { return 0.0f; }

		void set_primary(bool v) {}
		bool is_primary() const { return false; }

    private:
		std::string fdLoadFile(std::string path) { return ""; }
		bool _is_primary = false;
		float _confThres = 0.15f;
		float _nmsThres = 0.7f;
		std::shared_ptr<rga_interface_t> _rgaInterfacePtr = nullptr;
	};
}

#else

#include "rknn_api.h"

// Original implementation for non-Windows systems
namespace czcv_camera
{
	typedef struct 
	{
		Rectf rect;
		float score;
		int label;
	} DetectionResult;
	
    class Yolov10RKNN: public AbstarctModel
    {
    public:
		Yolov10RKNN() {LOGE("Yolov10RKNN::Yolov10RKNN\n");};
		~Yolov10RKNN() {LOGE("Yolov10RKNN::~Yolov10RKNN\n");};
		Status release() 
		{
			rknn_destroy_mem(ctx, input_mems[0]);
			for (int i = 0; i < 6; i++)
			{
				rknn_destroy_mem(ctx, output_mems[i]);
			}
			rknn_destroy(ctx);
			return CZCV_OK;
		}
		
        Status run(TrackerInputOutput &inputOutput);
		Status run_sub(const cv::Mat & bgr, std::vector<DetectionResult>& results_filtered);
		Status run_sub_rga(float scale, std::vector<DetectionResult>& results_filtered);
		Status run_sub_rga_qat(float scale, std::vector<DetectionResult>& results_filtered);
        Status init(std::vector<std::string> modelConfig,std::shared_ptr<rga_interface_t> &rgaInterfacePtr,czcv_model_type_t modelType);
		
		Status processPersonBoxGroup(TrackerInputOutput &inputOutput,
			const std::vector<size_t>& box_indices,
			const cv::Mat& bgr,
			int img_width,
			int img_height);

        void  conf_thres(float v){_confThres = v;}
        float conf_thres() const{return  _confThres;}

        void  nms_thres(float v){_nmsThres   = v;}
        float nms_thres() const {return  _nmsThres;}

		void set_primary(bool v) {_is_primary = v;}
		bool is_primary() const {return _is_primary;}

    private:
		std::string fdLoadFile(std::string path);
        float _confThres = 0.15f;
        float _nmsThres = 0.7f;
        int _inputChannel;
		int _inputWidth;
		int _inputHeight;
		int _boxNum;
		int _outputDim2;
		int _maxDet = 300;
		rknn_input_output_num io_num;
		std::vector<rknn_tensor_attr> input_attrs;
		std::vector<rknn_tensor_attr> output_attrs;
		rknn_context ctx;
		rknn_tensor_mem* input_mems[1];
		rknn_tensor_mem* output_mems[6];
		std::shared_ptr<rga_interface_t> _rgaInterfacePtr = nullptr;
		bool _is_primary = false;
		bool _rga_flag = true;
		bool _bqat = false;
		int _classNum;
		int _boxDim;

		int head_num = 3;
		int topK = 50;
		int class_num = 8;
		std::vector<float> meshgrid;
		std::vector<int> strides = {8, 16, 32};
		std::vector<std::vector<int>> map_size = {{80, 80}, {40, 40}, {20, 20}};
		
		void GenerateMeshgrid();
		std::vector<DetectBox> TopK(const std::vector<DetectBox>& detectResult);
		std::vector<DetectBox> postprocess(rknn_tensor_mem** outputs, int img_h, int img_w,float ratio,float dw,float dh);
		float calculateIoU(const DetectBox& box1, const DetectBox& box2);
		std::vector<DetectBox> applyNMS(const std::vector<DetectBox>& boxes, float nms_threshold);
		void filterPeaceGestures(TrackerInputOutput &inputOutput, int img_width);
	};
}

#endif
#endif //CZCV_HAND_RKNN_YOLOV10_H
