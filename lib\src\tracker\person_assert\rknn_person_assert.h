// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_MOBILE_INTELLIGENCE_RKNN_PERSON_ASSERT_H
#define CZCV_MOBILE_INTELLIGENCE_RKNN_PERSON_ASSERT_H


#include <vector>
#include <string>
#include <fstream>

#include <opencv2/opencv.hpp>
#include <base/common.h>
#include <base/status.h>
#include <base/abstract_model.h>
#include <tracker/base_tracker.h>
#ifndef _WIN32
#include "rknn_api.h"
#endif

namespace czcv_camera
{
    class PersonAssertRKNN : public PersonAssertModel
    {
    public:
        #ifdef _WIN32
        Status release() override
        { 
            return CZCV_OK;
        }

        Status init(std::vector<std::string> assertModelCfg,std::shared_ptr<rga_interface_t> &rgaInterfacePtr,czcv_model_type_t modelType) override
        {
            return CZCV_OK;
        }

        bool run(int in_phyaddr, cv::Rect roi_rect,int img_w,int img_h) override
        {
            return true;
        }
        #else
        Status release() override
        { 
            rknn_destroy_mem(ctx, input_mems[0]);
            rknn_destroy(ctx);
            
            if (rgamat_cls_pad.handle != NULL)
            {
                int ret = _rgaInterfacePtr->free(rgamat_cls_pad.handle);
                if (ret != 0)
                {
                    LOGE("cls release fail!");
                    return CZCV_MEM_ERR;
                }
                else
                {
                    LOGE("cls release success!");
                    rgamat_cls_pad.handle = nullptr;
                    return CZCV_OK;
                }     
            }
            return CZCV_OK;
        }

        Status init(std::vector<std::string> assertModelCfg,std::shared_ptr<rga_interface_t> &rgaInterfacePtr,czcv_model_type_t modelType) override;

        bool run(int in_phyaddr, cv::Rect roi_rect,int img_w,int img_h) override;
        #endif

        void  conf_thres(float v){_confThres = v;}
        float conf_thres() const{return  _confThres;}

    private:
        std::string fdLoadFile(std::string path);
        int _inputChannel;
		int _inputWidth;
		int _inputHeight;
        #ifndef _WIN32
        rknn_input_output_num io_num;
		std::vector<rknn_tensor_attr> input_attrs;
		std::vector<rknn_tensor_attr> output_attrs;
		rknn_context ctx;
		rknn_tensor_mem* input_mems[1];
        #endif
        float _confThres = 0.3;
        int _inputSize = 192;
        czcv_camera::RgaMat rgamat_cls_pad;
        std::shared_ptr<rga_interface_t> _rgaInterfacePtr = nullptr;
    };
}//namespace czcv_mobile

#endif //CZCV_MOBILE_INTELLIGENCE_RKNN_PERSON_ASSERT_H